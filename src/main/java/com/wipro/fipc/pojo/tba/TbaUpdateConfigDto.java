package com.wipro.fipc.pojo.tba;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wipro.fipc.entity.ProcessJobMapping;

import lombok.Data;

@Data
public class TbaUpdateConfigDto {

	private Long id;
	private Long processJobMappingId;
	private String tbaFieldName;
	private String eventName;
	private String updateName;
	private int panelId;
	private String basicInfo;
	private int clientId;
	private String metaData;
	private String subMetaData;
	private Object additionalMetaData;
	private String identifier;
	private String sequence;
	private String recordIdentifier;
	private String parNm;
	private String subKey;
	private String baseKey;
	private String jsonKey;
	private char addManualFlag;
	private char activeFlag;
	private String createdBy;
	private String updatedBy;
	private Date createdDate;
	private ProcessJobMapping processJobMapping;
	private char rerunFlag;
	private String tbaUpdateAction;
	private String[] overrideEdits;
	private String actLngDesc;
	private String jsonKeyId;
	private String transId;
	private String subMetaDataId;
	private String eventLongDesc;
	private int parentTbaUpdateId;
	private String[] estimateTbaInquiries;
	private Boolean groupRelatedPanels=false;
	private Boolean pickFromPendingEvent;
	private Boolean skipTrans;

}
